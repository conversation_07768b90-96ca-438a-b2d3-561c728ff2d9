using System;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using ReactiveUI;

namespace TDA4_Emulator.Models;

/// <summary>
/// Controls a single processor core process and manages its lifecycle
/// </summary>
public class CoreController : ReactiveObject, IDisposable
{
    private Process? _process;
    private bool _isRunning;
    private readonly CancellationTokenSource _cancellationTokenSource = new();

    /// <summary>
    /// The type of core this controller manages
    /// </summary>
    public CoreType CoreType { get; }

    /// <summary>
    /// Path to the binary executable for this core
    /// </summary>
    public string BinaryPath { get; set; } = string.Empty;

    /// <summary>
    /// Indicates whether the process is currently running
    /// </summary>
    public bool IsRunning
    {
        get => _isRunning;
        private set => this.RaiseAndSetIfChanged(ref _isRunning, value);
    }

    /// <summary>
    /// Collection of terminal output lines for this core
    /// </summary>
    public ObservableCollection<TerminalLine> TerminalOutput { get; } = new();

    /// <summary>
    /// Event raised when new output is received from the process
    /// </summary>
    public event EventHandler<TerminalLine>? OutputReceived;

    /// <summary>
    /// Event raised when the process exits
    /// </summary>
    public event EventHandler<int>? ProcessExited;

    public CoreController(CoreType coreType)
    {
        CoreType = coreType;
    }

    /// <summary>
    /// Validates that the binary path exists and is executable
    /// </summary>
    public bool ValidateBinaryPath()
    {
        if (string.IsNullOrWhiteSpace(BinaryPath))
            return false;

        if (!File.Exists(BinaryPath))
            return false;

        // Check if file has executable extension on Windows
        if (Environment.OSVersion.Platform == PlatformID.Win32NT)
        {
            var extension = Path.GetExtension(BinaryPath).ToLowerInvariant();
            return extension == ".exe" || extension == ".bat" || extension == ".cmd";
        }

        return true;
    }

    /// <summary>
    /// Starts the core process asynchronously
    /// </summary>
    public Task<bool> StartAsync()
    {
        if (IsRunning || !ValidateBinaryPath())
            return Task.FromResult(false);

        try
        {
            var startInfo = new ProcessStartInfo
            {
                FileName = BinaryPath,
                UseShellExecute = false,
                RedirectStandardInput = true,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                CreateNoWindow = true,
                WorkingDirectory = Path.GetDirectoryName(BinaryPath) ?? Environment.CurrentDirectory
            };

            _process = new Process { StartInfo = startInfo };

            // Set up event handlers for output
            _process.OutputDataReceived += OnOutputDataReceived;
            _process.ErrorDataReceived += OnErrorDataReceived;
            _process.Exited += OnProcessExited;
            _process.EnableRaisingEvents = true;

            // Start the process
            if (!_process.Start())
                return Task.FromResult(false);

            // Begin reading output asynchronously
            _process.BeginOutputReadLine();
            _process.BeginErrorReadLine();

            IsRunning = true;

            // Add system message about process start
            var startMessage = TerminalLine.CreateSystemMessage(CoreType, $"Process started (PID: {_process.Id})");
            AddTerminalLine(startMessage);

            return Task.FromResult(true);
        }
        catch (Exception ex)
        {
            var errorMessage = TerminalLine.CreateSystemMessage(CoreType, $"Failed to start process: {ex.Message}");
            AddTerminalLine(errorMessage);
            return Task.FromResult(false);
        }
    }

    /// <summary>
    /// Stops the core process asynchronously
    /// </summary>
    public Task StopAsync()
    {
        if (!IsRunning || _process == null)
            return Task.CompletedTask;

        try
        {
            // Try graceful shutdown first
            if (!_process.HasExited)
            {
                _process.StandardInput?.WriteLine("quit");
                _process.StandardInput?.WriteLine("exit");

                // Wait for graceful shutdown
                if (!_process.WaitForExit(5000))
                {
                    // Force kill if graceful shutdown fails
                    _process.Kill();
                }
            }

            var stopMessage = TerminalLine.CreateSystemMessage(CoreType, "Process stopped");
            AddTerminalLine(stopMessage);
        }
        catch (Exception ex)
        {
            var errorMessage = TerminalLine.CreateSystemMessage(CoreType, $"Error stopping process: {ex.Message}");
            AddTerminalLine(errorMessage);
        }
        finally
        {
            IsRunning = false;
            _process?.Dispose();
            _process = null;
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// Sends a command to the running process
    /// </summary>
    public async Task<bool> SendCommandAsync(string command)
    {
        if (!IsRunning || _process?.StandardInput == null)
            return false;

        try
        {
            await _process.StandardInput.WriteLineAsync(command);
            await _process.StandardInput.FlushAsync();

            var commandMessage = TerminalLine.CreateSystemMessage(CoreType, $"Command sent: {command}");
            AddTerminalLine(commandMessage);

            return true;
        }
        catch (Exception ex)
        {
            var errorMessage = TerminalLine.CreateSystemMessage(CoreType, $"Failed to send command: {ex.Message}");
            AddTerminalLine(errorMessage);
            return false;
        }
    }

    private void OnOutputDataReceived(object sender, DataReceivedEventArgs e)
    {
        if (!string.IsNullOrEmpty(e.Data))
        {
            var line = TerminalLine.CreateStandardOutput(CoreType, e.Data);
            AddTerminalLine(line);
        }
    }

    private void OnErrorDataReceived(object sender, DataReceivedEventArgs e)
    {
        if (!string.IsNullOrEmpty(e.Data))
        {
            var line = TerminalLine.CreateErrorOutput(CoreType, e.Data);
            AddTerminalLine(line);
        }
    }

    private void OnProcessExited(object? sender, EventArgs e)
    {
        var exitCode = _process?.ExitCode ?? -1;
        IsRunning = false;

        var exitMessage = TerminalLine.CreateSystemMessage(CoreType, $"Process exited with code: {exitCode}");
        AddTerminalLine(exitMessage);

        ProcessExited?.Invoke(this, exitCode);
    }

    private void AddTerminalLine(TerminalLine line)
    {
        // Use dispatcher to ensure UI thread safety
        Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
        {
            TerminalOutput.Add(line);
            OutputReceived?.Invoke(this, line);
        });
    }

    public void Dispose()
    {
        _cancellationTokenSource.Cancel();
        _cancellationTokenSource.Dispose();

        if (IsRunning)
        {
            StopAsync().Wait(TimeSpan.FromSeconds(5));
        }

        _process?.Dispose();
    }
}
