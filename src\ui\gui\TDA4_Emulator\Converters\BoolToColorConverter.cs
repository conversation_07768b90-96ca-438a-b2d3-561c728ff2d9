using System;
using System.Globalization;
using Avalonia.Data.Converters;
using Avalonia.Media;

namespace TDA4_Emulator.Converters;

/// <summary>
/// Converts boolean values to colors (e.g., for status indicators)
/// </summary>
public class BoolToColorConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is bool boolValue)
        {
            // Default colors: true = green, false = red
            return boolValue ? Brushes.LimeGreen : Brushes.Red;
        }
        
        return Brushes.Gray;
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
