#include "common.h"
#include <signal.h>

#ifdef _WIN32
#include <windows.h>
#include <io.h>
#include <fcntl.h>
#else
#include <unistd.h>
#endif

using namespace TDA4TestCore;

/**
 * @brief C7x Core Emulator - TI C7x Digital Signal Processor
 * 
 * The C7x core is optimized for digital signal processing, machine learning,
 * and computer vision workloads in the TDA4 SoC.
 */
class C7xCoreEmulator : public CoreEmulator {
private:
    int m_vectorUnits;
    bool m_mlAccelerator;
    std::vector<std::string> m_loadedKernels;
    int m_dmaChannels;

public:
    C7xCoreEmulator() : CoreEmulator(CoreType::C7x), m_vectorUnits(4), m_mlAccelerator(true), m_dmaChannels(8) {
        m_properties["core_frequency"] = "1.35GHz";
        m_properties["vector_units"] = std::to_string(m_vectorUnits);
        m_properties["ml_accelerator"] = m_mlAccelerator ? "enabled" : "disabled";
        m_properties["dma_channels"] = std::to_string(m_dmaChannels);
        m_properties["memory_bandwidth"] = "68.3 GB/s";
        
        // Initialize some default kernels
        m_loadedKernels = {"conv2d", "matrix_mult", "fft", "sobel_filter"};
    }

    void Start() override {
        CoreEmulator::Start();
        OutputMessage("C7x Core Features:");
        OutputMessage("  - High-performance DSP operations");
        OutputMessage("  - Vector processing units: " + std::to_string(m_vectorUnits));
        OutputMessage("  - Machine learning acceleration: " + m_properties["ml_accelerator"]);
        OutputMessage("  - Core frequency: " + m_properties["core_frequency"]);
        OutputMessage("  - Memory bandwidth: " + m_properties["memory_bandwidth"]);
        OutputMessage("  - DMA channels: " + std::to_string(m_dmaChannels));
        OutputMessage("DSP Runtime initialized");
    }

    bool ProcessCommand(const std::string& command) override {
        auto tokens = Split(Trim(command), ' ');
        if (tokens.empty()) return true;

        std::string cmd = tokens[0];
        std::transform(cmd.begin(), cmd.end(), cmd.begin(), ::tolower);

        // Handle C7x-specific commands
        if (cmd == "load_kernel" && tokens.size() >= 2) {
            HandleLoadKernel(tokens[1]);
        }
        else if (cmd == "execute_kernel" && tokens.size() >= 2) {
            HandleExecuteKernel(tokens);
        }
        else if (cmd == "list_kernels") {
            HandleListKernels();
        }
        else if (cmd == "dsp_benchmark") {
            HandleDspBenchmark();
        }
        else if (cmd == "ml_inference" && tokens.size() >= 2) {
            HandleMlInference(tokens[1]);
        }
        else if (cmd == "vector_op" && tokens.size() >= 2) {
            HandleVectorOperation(tokens);
        }
        else if (cmd == "dma_transfer" && tokens.size() >= 3) {
            HandleDmaTransfer(tokens);
        }
        else {
            // Fall back to base class command handling
            return CoreEmulator::ProcessCommand(command);
        }

        return true;
    }

protected:
    void HandleStatusCommand() override {
        CoreEmulator::HandleStatusCommand();
        OutputMessage("=== C7x Specific Status ===");
        OutputMessage("Vector Units: " + std::to_string(m_vectorUnits));
        OutputMessage("ML Accelerator: " + m_properties["ml_accelerator"]);
        OutputMessage("Loaded Kernels: " + std::to_string(m_loadedKernels.size()));
        OutputMessage("DMA Channels: " + std::to_string(m_dmaChannels));
        OutputMessage("Core Frequency: " + m_properties["core_frequency"]);
        OutputMessage("Memory Bandwidth: " + m_properties["memory_bandwidth"]);
    }

    void HandleHelpCommand() override {
        CoreEmulator::HandleHelpCommand();
        OutputMessage("=== C7x Specific Commands ===");
        OutputMessage("load_kernel <name>    - Load a DSP kernel");
        OutputMessage("execute_kernel <name> [args] - Execute a loaded kernel");
        OutputMessage("list_kernels         - List all loaded kernels");
        OutputMessage("dsp_benchmark        - Run DSP performance benchmark");
        OutputMessage("ml_inference <model> - Run ML inference");
        OutputMessage("vector_op <operation> - Execute vector operation");
        OutputMessage("dma_transfer <src> <dst> - Initiate DMA transfer");
    }

private:
    void HandleLoadKernel(const std::string& kernelName) {
        // Check if kernel already loaded
        auto it = std::find(m_loadedKernels.begin(), m_loadedKernels.end(), kernelName);
        if (it != m_loadedKernels.end()) {
            OutputMessage("Kernel '" + kernelName + "' is already loaded");
            return;
        }
        
        OutputMessage("Loading DSP kernel: " + kernelName);
        OutputMessage("Compiling kernel code...");
        OutputMessage("Optimizing for C7x architecture...");
        OutputMessage("Allocating kernel memory...");
        
        // Simulate kernel loading time
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
        
        m_loadedKernels.push_back(kernelName);
        OutputMessage("Kernel '" + kernelName + "' loaded successfully");
        OutputMessage("Kernel entry point: 0x" + std::to_string(0x10000000 + m_loadedKernels.size() * 0x1000));
    }

    void HandleExecuteKernel(const std::vector<std::string>& tokens) {
        std::string kernelName = tokens[1];
        
        // Check if kernel is loaded
        auto it = std::find(m_loadedKernels.begin(), m_loadedKernels.end(), kernelName);
        if (it == m_loadedKernels.end()) {
            OutputMessage("Kernel '" + kernelName + "' not found. Use 'load_kernel' first.");
            return;
        }
        
        OutputMessage("Executing kernel: " + kernelName);
        OutputMessage("Setting up vector units...");
        OutputMessage("Configuring DMA channels...");
        
        // Simulate different execution times based on kernel type
        int execTime = 50;
        if (kernelName.find("conv") != std::string::npos) execTime = 150;
        else if (kernelName.find("fft") != std::string::npos) execTime = 100;
        else if (kernelName.find("matrix") != std::string::npos) execTime = 200;
        
        std::this_thread::sleep_for(std::chrono::milliseconds(execTime));
        
        OutputMessage("Kernel execution completed");
        OutputMessage("Execution time: " + std::to_string(execTime) + "ms (simulated)");
        OutputMessage("Vector units utilized: " + std::to_string(m_vectorUnits));
    }

    void HandleListKernels() {
        OutputMessage("=== Loaded DSP Kernels ===");
        if (m_loadedKernels.empty()) {
            OutputMessage("No kernels loaded");
            return;
        }
        
        for (size_t i = 0; i < m_loadedKernels.size(); ++i) {
            std::string addr = "0x" + std::to_string(0x10000000 + (i + 1) * 0x1000);
            OutputMessage(std::to_string(i + 1) + ". " + m_loadedKernels[i] + " @ " + addr);
        }
        
        OutputMessage("Total kernels: " + std::to_string(m_loadedKernels.size()));
    }

    void HandleDspBenchmark() {
        OutputMessage("Starting C7x DSP benchmark...");
        OutputMessage("=== Benchmark Results ===");
        
        // Simulate various DSP operations
        OutputMessage("Matrix Multiplication (1024x1024): 45.2 GFLOPS");
        OutputMessage("FFT (2048-point): 12.8 ms");
        OutputMessage("Convolution (512x512): 23.1 ms");
        OutputMessage("Vector Addition (1M elements): 2.3 ms");
        OutputMessage("FIR Filter (1024 taps): 8.7 ms");
        
        OutputMessage("Overall Performance Score: 8.9/10");
        OutputMessage("Memory Bandwidth Utilization: 85%");
        OutputMessage("Vector Unit Efficiency: 92%");
    }

    void HandleMlInference(const std::string& modelName) {
        if (!m_mlAccelerator) {
            OutputMessage("ML accelerator is disabled");
            return;
        }
        
        OutputMessage("Running ML inference with model: " + modelName);
        OutputMessage("Loading model weights...");
        OutputMessage("Configuring ML accelerator...");
        OutputMessage("Optimizing computation graph...");
        
        // Simulate inference time based on model complexity
        int inferenceTime = 50;
        if (modelName.find("resnet") != std::string::npos) inferenceTime = 120;
        else if (modelName.find("yolo") != std::string::npos) inferenceTime = 80;
        else if (modelName.find("mobilenet") != std::string::npos) inferenceTime = 30;
        
        std::this_thread::sleep_for(std::chrono::milliseconds(inferenceTime));
        
        OutputMessage("Inference completed");
        OutputMessage("Inference time: " + std::to_string(inferenceTime) + "ms");
        OutputMessage("Accuracy: 94.7%");
        OutputMessage("Throughput: " + std::to_string(1000 / inferenceTime) + " FPS");
    }

    void HandleVectorOperation(const std::vector<std::string>& tokens) {
        std::string operation = tokens[1];
        
        OutputMessage("Executing vector operation: " + operation);
        OutputMessage("Configuring " + std::to_string(m_vectorUnits) + " vector units...");
        
        // Simulate vector operation
        std::this_thread::sleep_for(std::chrono::milliseconds(20));
        
        OutputMessage("Vector operation '" + operation + "' completed");
        OutputMessage("Elements processed: 1,048,576");
        OutputMessage("Processing rate: 52.4 GOPS");
    }

    void HandleDmaTransfer(const std::vector<std::string>& tokens) {
        std::string src = tokens[1];
        std::string dst = tokens[2];
        
        OutputMessage("Initiating DMA transfer: " + src + " -> " + dst);
        OutputMessage("Allocating DMA channel...");
        OutputMessage("Setting up transfer descriptors...");
        
        // Simulate DMA transfer
        std::this_thread::sleep_for(std::chrono::milliseconds(30));
        
        OutputMessage("DMA transfer completed");
        OutputMessage("Transfer size: 4MB");
        OutputMessage("Transfer rate: 133.3 MB/s");
        OutputMessage("DMA channel released");
    }
};

// Global emulator instance for signal handling
C7xCoreEmulator* g_emulator = nullptr;

// Signal handler for graceful shutdown
void SignalHandler(int signal) {
    if (g_emulator) {
        g_emulator->OutputMessage("Received signal " + std::to_string(signal) + " - shutting down gracefully");
        g_emulator->Stop();
    }
    exit(0);
}

int main() {
    // Set up signal handling
    signal(SIGINT, SignalHandler);
    signal(SIGTERM, SignalHandler);

#ifdef _WIN32
    // Set console to handle UTF-8 on Windows
    SetConsoleOutputCP(CP_UTF8);
    SetConsoleCP(CP_UTF8);
#endif

    // Create and start the C7x emulator
    C7xCoreEmulator emulator;
    g_emulator = &emulator;
    
    emulator.Start();

    // Main command processing loop
    std::string line;
    while (emulator.IsRunning() && std::getline(std::cin, line)) {
        if (!emulator.ProcessCommand(line)) {
            break;
        }
    }

    emulator.Stop();
    return 0;
}
