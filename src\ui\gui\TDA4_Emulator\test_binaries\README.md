# TDA4 Emulator Test Binaries

This directory contains C++ test programs that simulate the different processor cores in the TDA4 SoC for testing and development of the TDA4 Emulator UI application.

## Overview

The test binaries provide mock implementations of the three main processor cores in the TDA4 system:

- **R5F Core** (`tda4_r5f_core`) - Real-time Processing Unit
- **A72 Core** (`tda4_a72_core`) - ARM Cortex-A72 Application Processor  
- **C7x Core** (`tda4_c7x_core`) - TI C7x Digital Signal Processor

Each program simulates the communication interface and core-specific functionality that the UI application expects when controlling actual TDA4 hardware.

## Building the Test Binaries

### Prerequisites

- CMake 3.16 or later
- C++17 compatible compiler (GCC, Clang, or MSVC)
- Make or Ninja build system

### Build Instructions

1. **Create build directory:**
   ```bash
   mkdir build
   cd build
   ```

2. **Configure with CMake:**
   ```bash
   # For Release build
   cmake -DCMAKE_BUILD_TYPE=Release ..
   
   # For Debug build
   cmake -DCMAKE_BUILD_TYPE=Debug ..
   ```

3. **Build all targets:**
   ```bash
   cmake --build .
   
   # Or build specific core
   cmake --build . --target tda4_r5f_core
   ```

4. **Install (optional):**
   ```bash
   cmake --install . --prefix /path/to/install
   ```

### Build Output

The compiled executables will be located in:
- `build/bin/tda4_r5f_core.exe` (Windows) or `build/bin/tda4_r5f_core` (Linux)
- `build/bin/tda4_a72_core.exe` (Windows) or `build/bin/tda4_a72_core` (Linux)
- `build/bin/tda4_c7x_core.exe` (Windows) or `build/bin/tda4_c7x_core` (Linux)

## Usage

### Running Test Binaries

Each test binary can be run standalone for testing:

```bash
# Run R5F core emulator
./build/bin/tda4_r5f_core

# Run A72 core emulator  
./build/bin/tda4_a72_core

# Run C7x core emulator
./build/bin/tda4_c7x_core
```

### Communication Protocol

The test binaries communicate via stdin/stdout:

- **Input**: Commands sent via stdin (one per line)
- **Output**: Responses and status messages via stdout
- **Errors**: Error messages via stderr

### Common Commands

All cores support these basic commands:

- `status` - Display core status information
- `help` - Show available commands
- `ping` - Test connectivity (responds with "pong")
- `ipc_send <target_core> <message>` - Send IPC message to another core
- `quit` or `exit` - Shutdown the emulator

### Core-Specific Commands

#### R5F Core Commands
- `rt_task <name>` - Execute a real-time task
- `interrupt <id>` - Simulate interrupt handling
- `safety_check` - Perform safety system check
- `set_frequency <freq>` - Set core operating frequency

#### A72 Core Commands
- `launch_app <name>` - Launch an application
- `kill_process <name>` - Terminate a process
- `list_processes` - List all running processes
- `cpu_load` - Show current CPU utilization
- `memory_info` - Display memory usage information
- `set_governor <mode>` - Set CPU frequency governor

#### C7x Core Commands
- `load_kernel <name>` - Load a DSP kernel
- `execute_kernel <name>` - Execute a loaded kernel
- `list_kernels` - List all loaded kernels
- `dsp_benchmark` - Run DSP performance benchmark
- `ml_inference <model>` - Run ML inference
- `vector_op <operation>` - Execute vector operation
- `dma_transfer <src> <dst>` - Initiate DMA transfer

## Integration with TDA4 Emulator UI

### Binary Path Configuration

In the TDA4 Emulator UI application:

1. Navigate to the binary selection interface
2. Set the binary paths to point to the built executables:
   - R5F Core: `path/to/build/bin/tda4_r5f_core.exe`
   - A72 Core: `path/to/build/bin/tda4_a72_core.exe`
   - C7x Core: `path/to/build/bin/tda4_c7x_core.exe`

### Expected Behavior

When launched by the UI:

1. **Startup**: Each core outputs identification and feature information
2. **Command Processing**: Cores respond to commands sent via stdin
3. **IPC Simulation**: Cores can simulate inter-processor communication
4. **Graceful Shutdown**: Cores respond to quit/exit commands

### Output Format

The test binaries output messages in a format compatible with the UI's TerminalLine parsing:

```
TDA4 R5F Core Emulator started
Version: 1.0.0
Process ID: 12345
Ready to receive commands...
```

## Development and Testing

### Adding New Commands

To add new commands to a core emulator:

1. Add command handling in the `ProcessCommand()` method
2. Implement the command handler function
3. Update the `HandleHelpCommand()` method
4. Test the command manually

### Debugging

For debugging issues:

1. Run the binary manually and test commands interactively
2. Check stderr output for error messages
3. Use the `status` command to verify core state
4. Enable debug builds for additional logging

### Testing IPC Communication

To test IPC between cores:

1. Start multiple core emulators in separate terminals
2. Use the `ipc_send` command to simulate communication
3. Verify message handling and responses

## File Structure

```
test_binaries/
├── CMakeLists.txt          # Build configuration
├── README.md              # This file
└── src/
    ├── common.h           # Shared utilities and base classes
    ├── tda4_r5f_core.cpp  # R5F core emulator
    ├── tda4_a72_core.cpp  # A72 core emulator
    └── tda4_c7x_core.cpp  # C7x core emulator
```

## Troubleshooting

### Build Issues

- Ensure CMake version is 3.16 or later
- Verify C++17 compiler support
- Check that all source files are present

### Runtime Issues

- Verify executable permissions on Linux/macOS
- Check that stdin/stdout redirection is working
- Ensure the UI application can find the binary paths

### Communication Issues

- Test binaries manually with interactive input
- Verify command syntax matches expected format
- Check for proper line endings in commands
