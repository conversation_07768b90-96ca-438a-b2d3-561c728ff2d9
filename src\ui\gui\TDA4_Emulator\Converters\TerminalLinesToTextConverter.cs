using System;
using System.Collections.ObjectModel;
using System.Globalization;
using System.Linq;
using System.Text;
using Avalonia.Data.Converters;
using TDA4_Emulator.Models;

namespace TDA4_Emulator.Converters;

/// <summary>
/// Converts a collection of TerminalLine objects to formatted text for display
/// </summary>
public class TerminalLinesToTextConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is ObservableCollection<TerminalLine> lines)
        {
            var sb = new StringBuilder();
            
            foreach (var line in lines)
            {
                sb.AppendLine(line.FormattedText);
            }
            
            return sb.ToString();
        }
        
        return string.Empty;
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
