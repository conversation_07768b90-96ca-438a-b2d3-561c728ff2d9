@echo off
REM Build script for TDA4 Test Binaries on Windows

echo Building TDA4 Test Binaries...

REM Create build directory
if not exist build mkdir build
cd build

REM Configure with CMake
echo Configuring with CMake...
cmake -DCMAKE_BUILD_TYPE=Release ..
if %ERRORLEVEL% neq 0 (
    echo CMake configuration failed!
    pause
    exit /b 1
)

REM Build all targets
echo Building all targets...
cmake --build . --config Release
if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo Build completed successfully!
echo.
echo Executables are located in:
echo   build\bin\Release\tda4_r5f_core.exe
echo   build\bin\Release\tda4_a72_core.exe
echo   build\bin\Release\tda4_c7x_core.exe
echo.

pause
