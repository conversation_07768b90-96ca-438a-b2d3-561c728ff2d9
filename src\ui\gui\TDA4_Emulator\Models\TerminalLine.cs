using System;
using Avalonia.Media;

namespace TDA4_Emulator.Models;

/// <summary>
/// Represents a line of terminal output with formatting information
/// </summary>
public class TerminalLine
{
    /// <summary>
    /// The timestamp when this line was created
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// The text content of the line
    /// </summary>
    public string Text { get; set; } = string.Empty;

    /// <summary>
    /// The type of output (standard output, error, or system message)
    /// </summary>
    public TerminalLineType LineType { get; set; }

    /// <summary>
    /// The core that generated this output
    /// </summary>
    public CoreType SourceCore { get; set; }

    /// <summary>
    /// Gets the formatted text with timestamp
    /// </summary>
    public string FormattedText => $"[{Timestamp:HH:mm:ss.fff}] [{SourceCore.GetShortName()}] {Text}";

    /// <summary>
    /// Gets the color for this line based on its type
    /// </summary>
    public IBrush TextBrush => LineType switch
    {
        TerminalLineType.StandardOutput => new SolidColorBrush(Color.FromRgb(0x00, 0x64, 0x00)), // Dark green #006400
        TerminalLineType.ErrorOutput => new SolidColorBrush(Color.FromRgb(0xDC, 0x14, 0x3C)),    // Red #DC143C
        TerminalLineType.SystemMessage => new SolidColorBrush(Color.FromRgb(0x00, 0x00, 0x80)),  // Dark blue #000080
        TerminalLineType.IpcMessage => new SolidColorBrush(Color.FromRgb(0x80, 0x00, 0x80)),     // Dark purple #800080
        _ => Brushes.Black
    };

    /// <summary>
    /// Creates a new terminal line for standard output
    /// </summary>
    public static TerminalLine CreateStandardOutput(CoreType sourceCore, string text)
    {
        return new TerminalLine
        {
            Timestamp = DateTime.Now,
            Text = text,
            LineType = TerminalLineType.StandardOutput,
            SourceCore = sourceCore
        };
    }

    /// <summary>
    /// Creates a new terminal line for error output
    /// </summary>
    public static TerminalLine CreateErrorOutput(CoreType sourceCore, string text)
    {
        return new TerminalLine
        {
            Timestamp = DateTime.Now,
            Text = text,
            LineType = TerminalLineType.ErrorOutput,
            SourceCore = sourceCore
        };
    }

    /// <summary>
    /// Creates a new terminal line for system messages
    /// </summary>
    public static TerminalLine CreateSystemMessage(CoreType sourceCore, string text)
    {
        return new TerminalLine
        {
            Timestamp = DateTime.Now,
            Text = text,
            LineType = TerminalLineType.SystemMessage,
            SourceCore = sourceCore
        };
    }

    /// <summary>
    /// Creates a new terminal line for IPC messages
    /// </summary>
    public static TerminalLine CreateIpcMessage(CoreType sourceCore, CoreType destinationCore, string message)
    {
        return new TerminalLine
        {
            Timestamp = DateTime.Now,
            Text = $"IPC -> {destinationCore.GetShortName()}: {message}",
            LineType = TerminalLineType.IpcMessage,
            SourceCore = sourceCore
        };
    }
}

/// <summary>
/// Types of terminal output lines
/// </summary>
public enum TerminalLineType
{
    /// <summary>
    /// Standard output from the process
    /// </summary>
    StandardOutput,

    /// <summary>
    /// Error output from the process
    /// </summary>
    ErrorOutput,

    /// <summary>
    /// System messages from the emulator control panel
    /// </summary>
    SystemMessage,

    /// <summary>
    /// Inter-processor communication messages
    /// </summary>
    IpcMessage
}
