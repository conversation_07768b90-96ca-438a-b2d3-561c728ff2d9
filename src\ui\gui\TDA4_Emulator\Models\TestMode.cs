using System.Collections.Generic;

namespace TDA4_Emulator.Models;

/// <summary>
/// Represents different test modes available in the TDA4 Emulator
/// </summary>
public enum TestMode
{
    /// <summary>
    /// Inter-Processor Communication test mode
    /// </summary>
    IPC,
    
    /// <summary>
    /// Test Mode 1 - placeholder for future functionality
    /// </summary>
    TestMode1,
    
    /// <summary>
    /// Test Mode 2 - placeholder for future functionality
    /// </summary>
    TestMode2
}

/// <summary>
/// Extension methods for TestMode enum
/// </summary>
public static class TestModeExtensions
{
    /// <summary>
    /// Gets the display name for the test mode
    /// </summary>
    public static string GetDisplayName(this TestMode testMode)
    {
        return testMode switch
        {
            TestMode.IPC => "IPC",
            TestMode.TestMode1 => "TestMode1",
            TestMode.TestMode2 => "TestMode2",
            _ => testMode.ToString()
        };
    }
    
    /// <summary>
    /// Gets the description for the test mode
    /// </summary>
    public static string GetDescription(this TestMode testMode)
    {
        return testMode switch
        {
            TestMode.IPC => "Inter-Processor Communication testing",
            TestMode.TestMode1 => "Test Mode 1 - Future functionality",
            TestMode.TestMode2 => "Test Mode 2 - Future functionality",
            _ => "Unknown test mode"
        };
    }
    
    /// <summary>
    /// Gets all available test modes
    /// </summary>
    public static IEnumerable<TestMode> GetAllTestModes()
    {
        return new[] { TestMode.IPC, TestMode.TestMode1, TestMode.TestMode2 };
    }
}
