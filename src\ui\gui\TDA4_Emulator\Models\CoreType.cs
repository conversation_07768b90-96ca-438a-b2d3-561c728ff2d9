using System.Collections.Generic;

namespace TDA4_Emulator.Models;

/// <summary>
/// Represents the different processor cores in the TDA4 emulator
/// </summary>
public enum CoreType
{
    /// <summary>
    /// R5F (Real-time Processing Unit) Core
    /// </summary>
    R5F,

    /// <summary>
    /// A72 (ARM Cortex-A72) Application Processor Core
    /// </summary>
    A72,

    /// <summary>
    /// C7x (TI C7x DSP) Digital Signal Processor Core
    /// </summary>
    C7x,

    /// <summary>
    /// All cores (used for broadcast operations)
    /// </summary>
    All
}

/// <summary>
/// Extension methods for CoreType enum
/// </summary>
public static class CoreTypeExtensions
{
    /// <summary>
    /// Gets the display name for the core type
    /// </summary>
    public static string GetDisplayName(this CoreType coreType)
    {
        return coreType switch
        {
            CoreType.R5F => "R5F Core",
            CoreType.A72 => "A72 Core",
            CoreType.C7x => "C7x Core",
            CoreType.All => "All Cores",
            _ => coreType.ToString()
        };
    }

    /// <summary>
    /// Gets the short name for the core type (used in logging)
    /// </summary>
    public static string GetShortName(this CoreType coreType)
    {
        return coreType switch
        {
            CoreType.R5F => "R5F",
            CoreType.A72 => "A72",
            CoreType.C7x => "C7x",
            CoreType.All => "ALL",
            _ => coreType.ToString()
        };
    }

    /// <summary>
    /// Gets all individual core types (excludes All)
    /// </summary>
    public static IEnumerable<CoreType> GetIndividualCores()
    {
        return new[] { CoreType.R5F, CoreType.A72, CoreType.C7x };
    }
}
