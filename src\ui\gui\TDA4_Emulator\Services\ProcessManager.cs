using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using TDA4_Emulator.Models;
using ReactiveUI;

namespace TDA4_Emulator.Services;

/// <summary>
/// Manages multiple core controllers and provides centralized process management
/// </summary>
public class ProcessManager : ReactiveObject, IDisposable
{
    private readonly Dictionary<CoreType, CoreController> _coreControllers;
    private bool _isAnyProcessRunning;

    /// <summary>
    /// Collection of all terminal output from all cores
    /// </summary>
    public ObservableCollection<TerminalLine> AllTerminalOutput { get; } = new();

    /// <summary>
    /// Indicates whether any core process is currently running
    /// </summary>
    public bool IsAnyProcessRunning
    {
        get => _isAnyProcessRunning;
        private set => this.RaiseAndSetIfChanged(ref _isAnyProcessRunning, value);
    }

    /// <summary>
    /// Event raised when any process starts or stops
    /// </summary>
    public event EventHandler<ProcessStatusChangedEventArgs>? ProcessStatusChanged;

    /// <summary>
    /// Event raised when new output is received from any core
    /// </summary>
    public event EventHandler<TerminalLine>? OutputReceived;

    public ProcessManager()
    {
        _coreControllers = new Dictionary<CoreType, CoreController>();

        // Initialize controllers for each core type
        foreach (var coreType in CoreTypeExtensions.GetIndividualCores())
        {
            var controller = new CoreController(coreType);
            controller.OutputReceived += OnCoreOutputReceived;
            controller.ProcessExited += OnCoreProcessExited;
            _coreControllers[coreType] = controller;
        }
    }

    /// <summary>
    /// Gets the controller for a specific core type
    /// </summary>
    public CoreController GetCoreController(CoreType coreType)
    {
        if (coreType == CoreType.All)
            throw new ArgumentException("Cannot get controller for 'All' core type", nameof(coreType));

        return _coreControllers[coreType];
    }

    /// <summary>
    /// Gets all core controllers
    /// </summary>
    public IEnumerable<CoreController> GetAllControllers()
    {
        return _coreControllers.Values;
    }

    /// <summary>
    /// Sets the binary path for a specific core
    /// </summary>
    public void SetBinaryPath(CoreType coreType, string path)
    {
        if (coreType != CoreType.All && _coreControllers.TryGetValue(coreType, out var controller))
        {
            controller.BinaryPath = path;
        }
    }

    /// <summary>
    /// Validates all binary paths
    /// </summary>
    public Dictionary<CoreType, bool> ValidateAllBinaryPaths()
    {
        var results = new Dictionary<CoreType, bool>();

        foreach (var kvp in _coreControllers)
        {
            results[kvp.Key] = kvp.Value.ValidateBinaryPath();
        }

        return results;
    }

    /// <summary>
    /// Starts a specific core process
    /// </summary>
    public async Task<bool> StartCoreAsync(CoreType coreType)
    {
        if (coreType == CoreType.All)
            throw new ArgumentException("Cannot start 'All' core type individually", nameof(coreType));

        if (_coreControllers.TryGetValue(coreType, out var controller))
        {
            var result = await controller.StartAsync();
            UpdateRunningStatus();
            return result;
        }

        return false;
    }

    /// <summary>
    /// Starts all configured core processes
    /// </summary>
    public async Task<Dictionary<CoreType, bool>> StartAllAsync()
    {
        var results = new Dictionary<CoreType, bool>();

        foreach (var kvp in _coreControllers)
        {
            if (!string.IsNullOrWhiteSpace(kvp.Value.BinaryPath))
            {
                results[kvp.Key] = await kvp.Value.StartAsync();
            }
            else
            {
                results[kvp.Key] = false;
            }
        }

        UpdateRunningStatus();
        return results;
    }

    /// <summary>
    /// Stops all running core processes
    /// </summary>
    public async Task StopAllAsync()
    {
        var stopTasks = _coreControllers.Values
            .Where(c => c.IsRunning)
            .Select(c => c.StopAsync());

        await Task.WhenAll(stopTasks);
        UpdateRunningStatus();
    }

    /// <summary>
    /// Sends an IPC message from source cores to destination cores
    /// </summary>
    public async Task<bool> SendIpcMessageAsync(
        IEnumerable<CoreType> sourceCores,
        IEnumerable<CoreType> destinationCores,
        string message)
    {
        if (string.IsNullOrWhiteSpace(message))
            return false;

        var sourceList = sourceCores.ToList();
        var destList = destinationCores.ToList();

        // Expand "All" selections
        if (sourceList.Contains(CoreType.All))
        {
            sourceList = CoreTypeExtensions.GetIndividualCores().ToList();
        }

        if (destList.Contains(CoreType.All))
        {
            destList = CoreTypeExtensions.GetIndividualCores().ToList();
        }

        bool anySuccess = false;

        foreach (var sourceCore in sourceList)
        {
            if (_coreControllers.TryGetValue(sourceCore, out var sourceController) && sourceController.IsRunning)
            {
                foreach (var destCore in destList)
                {
                    // Create IPC message format
                    var ipcCommand = $"ipc_send {destCore.GetShortName()} {message}";

                    if (await sourceController.SendCommandAsync(ipcCommand))
                    {
                        // Log the IPC message
                        var ipcLine = TerminalLine.CreateIpcMessage(sourceCore, destCore, message);
                        AddToAllTerminalOutput(ipcLine);
                        anySuccess = true;
                    }
                }
            }
        }

        return anySuccess;
    }

    /// <summary>
    /// Sends a command to specific cores
    /// </summary>
    public async Task<Dictionary<CoreType, bool>> SendCommandTocores(
        IEnumerable<CoreType> targetCores,
        string command)
    {
        var results = new Dictionary<CoreType, bool>();
        var coreList = targetCores.ToList();

        // Expand "All" selection
        if (coreList.Contains(CoreType.All))
        {
            coreList = CoreTypeExtensions.GetIndividualCores().ToList();
        }

        foreach (var coreType in coreList)
        {
            if (_coreControllers.TryGetValue(coreType, out var controller))
            {
                results[coreType] = await controller.SendCommandAsync(command);
            }
            else
            {
                results[coreType] = false;
            }
        }

        return results;
    }

    /// <summary>
    /// Gets terminal output for a specific core
    /// </summary>
    public ObservableCollection<TerminalLine> GetCoreTerminalOutput(CoreType coreType)
    {
        if (coreType == CoreType.All)
            return AllTerminalOutput;

        return _coreControllers.TryGetValue(coreType, out var controller)
            ? controller.TerminalOutput
            : new ObservableCollection<TerminalLine>();
    }

    /// <summary>
    /// Clears terminal output for a specific core or all cores
    /// </summary>
    public void ClearTerminalOutput(CoreType coreType)
    {
        if (coreType == CoreType.All)
        {
            AllTerminalOutput.Clear();
            foreach (var controller in _coreControllers.Values)
            {
                controller.TerminalOutput.Clear();
            }
        }
        else if (_coreControllers.TryGetValue(coreType, out var controller))
        {
            controller.TerminalOutput.Clear();
        }
    }

    private void OnCoreOutputReceived(object? sender, TerminalLine line)
    {
        AddToAllTerminalOutput(line);
        OutputReceived?.Invoke(this, line);
    }

    private void OnCoreProcessExited(object? sender, int exitCode)
    {
        UpdateRunningStatus();

        if (sender is CoreController controller)
        {
            ProcessStatusChanged?.Invoke(this, new ProcessStatusChangedEventArgs
            {
                CoreType = controller.CoreType,
                IsRunning = false,
                ExitCode = exitCode
            });
        }
    }

    private void AddToAllTerminalOutput(TerminalLine line)
    {
        Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
        {
            AllTerminalOutput.Add(line);
        });
    }

    private void UpdateRunningStatus()
    {
        IsAnyProcessRunning = _coreControllers.Values.Any(c => c.IsRunning);
    }

    public void Dispose()
    {
        foreach (var controller in _coreControllers.Values)
        {
            controller.Dispose();
        }
        _coreControllers.Clear();
    }
}

/// <summary>
/// Event arguments for process status changes
/// </summary>
public class ProcessStatusChangedEventArgs : EventArgs
{
    public CoreType CoreType { get; set; }
    public bool IsRunning { get; set; }
    public int? ExitCode { get; set; }
}
