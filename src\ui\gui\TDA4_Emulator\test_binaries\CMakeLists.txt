cmake_minimum_required(VERSION 3.16)
project(TDA4_TestBinaries VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set output directories
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# Compiler-specific options
if(MSVC)
    add_compile_options(/W4)
else()
    add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# Include directories
include_directories(src)

# Define core executables
set(CORE_TYPES r5f a72 c7x)

foreach(CORE_TYPE ${CORE_TYPES})
    set(TARGET_NAME tda4_${CORE_TYPE}_core)
    add_executable(${TARGET_NAME} src/tda4_${CORE_TYPE}_core.cpp)
    
    # Set target properties
    set_target_properties(${TARGET_NAME} PROPERTIES
        OUTPUT_NAME ${TARGET_NAME}
        DEBUG_POSTFIX "_d"
    )
    
    # Platform-specific settings
    if(WIN32)
        set_target_properties(${TARGET_NAME} PROPERTIES
            WIN32_EXECUTABLE FALSE  # Console application
        )
    endif()
endforeach()

# Custom target to build all cores
add_custom_target(all_cores DEPENDS 
    tda4_r5f_core 
    tda4_a72_core 
    tda4_c7x_core
)

# Installation rules
install(TARGETS tda4_r5f_core tda4_a72_core tda4_c7x_core
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

# Print build information
message(STATUS "TDA4 Test Binaries Configuration:")
message(STATUS "  Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Output Directory: ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}")
