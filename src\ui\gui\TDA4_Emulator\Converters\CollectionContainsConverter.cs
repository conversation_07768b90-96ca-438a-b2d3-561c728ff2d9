using System;
using System.Collections;
using System.Globalization;
using Avalonia.Data.Converters;

namespace TDA4_Emulator.Converters;

/// <summary>
/// Converter that checks if a collection contains a specific item
/// </summary>
public class CollectionContainsConverter : IValueConverter
{
    public static readonly CollectionContainsConverter Instance = new();

    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is IEnumerable enumerable && parameter != null)
        {
            foreach (var item in enumerable)
            {
                if (Equals(item, parameter))
                    return true;
            }
        }

        return false;
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
