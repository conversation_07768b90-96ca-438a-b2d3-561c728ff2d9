<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:models="using:TDA4_Emulator.Models"
             xmlns:converters="using:TDA4_Emulator.Converters"
             x:Class="TDA4_Emulator.Controls.MultiSelectComboBox"
             x:DataType="controls:MultiSelectComboBox"
             xmlns:controls="using:TDA4_Emulator.Controls">

    <UserControl.Resources>
        <converters:CoreTypeToDisplayNameConverter x:Key="CoreTypeToDisplayNameConverter"/>
    </UserControl.Resources>

    <Border BorderBrush="#CCCCCC" BorderThickness="1" CornerRadius="4" Background="White">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- ComboBox Header (shows selected items) -->
            <Button Grid.Row="0" Name="HeaderButton"
                    Background="Transparent" BorderThickness="0"
                    Padding="8,6" HorizontalContentAlignment="Left"
                    Click="HeaderButton_Click">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0" Text="{Binding DisplayText}"
                               VerticalAlignment="Center" TextTrimming="CharacterEllipsis"/>

                    <Path Grid.Column="1" Data="M 0 0 L 4 4 L 8 0 Z"
                          Fill="#666666" VerticalAlignment="Center" Margin="8,0,0,0"/>
                </Grid>
            </Button>

            <!-- Dropdown Panel -->
            <Border Grid.Row="1" Name="DropdownPanel"
                    Background="White" BorderBrush="#CCCCCC" BorderThickness="1,0,1,1"
                    CornerRadius="0,0,4,4" IsVisible="False"
                    MaxHeight="200">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="4">
                        <!-- All option -->
                        <CheckBox Content="All Cores" Margin="4,2"
                                  IsChecked="{Binding IsAllSelected}"
                                  Command="{Binding ToggleAllCommand}"/>

                        <Separator Margin="0,4"/>

                        <!-- Individual core options -->
                        <ItemsControl ItemsSource="{Binding IndividualCores}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <CheckBox Content="{Binding Converter={StaticResource CoreTypeToDisplayNameConverter}}"
                                              Margin="4,2"
                                              IsEnabled="{Binding $parent[controls:MultiSelectComboBox].AreIndividualCoresEnabled}"
                                              Command="{Binding $parent[controls:MultiSelectComboBox].ToggleCoreCommand}"
                                              CommandParameter="{Binding}"/>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                </ScrollViewer>
            </Border>
        </Grid>
    </Border>

</UserControl>
