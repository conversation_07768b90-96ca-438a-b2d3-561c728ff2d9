
# TDA4 Emulator Control Panel

A cross-platform desktop application built with **Avalonia UI 11.x** and **ReactiveUI** for controlling TDA4 Emulator simulations. The application provides a comprehensive interface for managing multiple processor cores (R5F, A72, C7x) with real-time terminal output and inter-processor communication capabilities.

## Features

### 🎯 **Core Management**
- **Multi-Core Support**: Manage R5F, A72, and C7x processor cores simultaneously
- **Process Lifecycle**: Start, stop, and monitor core processes with real-time status updates
- **Binary Configuration**: Easy configuration of executable paths for each core type
- **Cross-Platform**: Runs on Windows, macOS, and Linux with .NET 9.0

### 🖥️ **User Interface**
- **Modern Design**: Bordeaux red and light gray theme with professional styling
- **Responsive Layout**: Resizable interface with minimum 1024x768 support, default Full HD
- **Real-Time Terminal**: Multi-tabbed terminal output with color-coded messages
- **Status Indicators**: Visual feedback for simulation state and core status

### 📡 **Inter-Processor Communication**
- **Direct Command Dispatch**: Send raw commands directly to core processes via stdin
- **Multi-Core Selection**: Choose source and destination cores with intuitive checkboxes
- **Real-Time Communication**: Immediate command execution with live terminal feedback
- **Message Logging**: All communications are logged with timestamps and core identification

### 🔧 **Advanced Features**
- **Test Mode Selection**: Dynamic interface switching between IPC, TestMode1, and TestMode2
- **File Browser Integration**: Native file dialogs with platform-specific executable filters
- **Input Validation**: Real-time validation of binary paths and commands
- **Error Handling**: Comprehensive error handling with user-friendly feedback
- **Logging System**: Detailed logging to file for debugging and audit trails
- **Enhanced Help System**: Comprehensive help dialog with detailed usage instructions

## Architecture

### **MVVM Pattern with ReactiveUI**
```
├── Models/
│   ├── CoreType.cs              # Enum for processor core types
│   ├── TerminalLine.cs          # Terminal output data model
│   └── CoreController.cs        # Individual core process management
├── ViewModels/
│   ├── ViewModelBase.cs         # Base class with IDisposable
│   └── MainWindowViewModel.cs   # Main application logic
├── Views/
│   ├── MainWindow.axaml         # Main application UI
│   └── HelpDialog.axaml         # Help documentation dialog
├── Services/
│   ├── ProcessManager.cs        # Multi-core process orchestration
│   └── LoggingService.cs        # Centralized logging
├── Converters/
│   ├── CoreTypeToDisplayNameConverter.cs
│   ├── BoolToColorConverter.cs
│   └── TerminalLinesToTextConverter.cs
└── test_binaries/               # Mock executables for testing
    ├── r5f_core_mock.bat
    ├── a72_core_mock.bat
    └── c71x_core_mock.bat
```

## Getting Started

### **Prerequisites**
- .NET 9.0 SDK or later
- Windows 10/11, macOS 10.15+, or Linux (Ubuntu 18.04+)
- 4GB RAM minimum, 100MB disk space

### **Building the Application**
```bash
# Navigate to the project directory
cd src/ui/gui/TDA4_Emulator

# Restore dependencies
dotnet restore

# Build the application
dotnet build

# Run the application
dotnet run
```

### **Using Test Binaries**
The project includes mock binary files for testing in the `test_binaries/` directory. These simulate the behavior of actual TDA4 core executables and can be used to test the application functionality without real hardware.

## Usage Guide

### **1. Configure Binary Paths**
1. Click the "Browse" buttons next to each core type (R5F, A72, C71x)
2. Select the appropriate executable files (use test_binaries for testing)
3. Validation will show error messages for invalid paths

### **2. Select Test Mode**
1. Choose from the Test Mode dropdown: IPC, TestMode1, or TestMode2
2. The interface will dynamically switch to show relevant controls
3. IPC mode provides core selection and command input interface

### **3. Start Simulation**
1. Ensure at least one valid binary path is configured
2. Click "Start Simulation" to launch all configured cores
3. Monitor the status bar for startup progress and any errors

### **4. Send Commands (IPC Mode)**
1. Select source and destination cores using the checkboxes
2. Enter your command/message in the text area
3. Click "Send IPC" to send the raw command directly to core processes
4. Commands are transmitted via stdin without intermediate formatting

### **5. Monitor Output**
- **Three-Column Terminal Layout**:
  - R5F Core Terminal (left)
  - A72 Core Terminal (center)
  - C71x Core Terminal (right)
- **Color Coding**:
  - 🟢 Green: Standard output
  - 🔴 Red: Error output
  - 🟡 Yellow: System messages
  - 🔵 Cyan: IPC messages
- **Individual Clear Buttons**: Clear specific terminal outputs as needed

## Technical Implementation

### **Key Dependencies**
```xml
<PackageReference Include="Avalonia" Version="11.3.0" />
<PackageReference Include="Avalonia.ReactiveUI" Version="11.3.0" />
<PackageReference Include="ReactiveUI.Fody" Version="19.5.41" />
<PackageReference Include="System.Reactive" Version="6.0.1" />
```

### **Process Communication**
- **Local Process IO**: Uses `Process.StandardInput/Output/Error` redirection
- **No Network Dependencies**: All communication via local process pipes
- **Thread-Safe Updates**: UI updates via `Dispatcher.UIThread.InvokeAsync`
- **Graceful Shutdown**: Attempts graceful termination before force kill

### **Cross-Platform Support**
- **File Paths**: Uses `Path.Combine()` for platform-agnostic paths
- **Executable Detection**: Windows (.exe, .bat, .cmd) vs Unix (any file)
- **Font Fallbacks**: Consolas (Windows), Monaco (macOS), monospace (Linux)

## Troubleshooting

### **Common Issues**

**Build Errors:**
- Ensure .NET 9.0 SDK is installed
- Run `dotnet restore` to update dependencies

**Process Launch Failures:**
- Verify binary paths are correct and files exist
- Check file permissions (executable on Unix systems)
- Review logs in `logs/tda4_emulator_YYYYMMDD.log`

**UI Responsiveness:**
- All process operations are asynchronous
- Use "Clear Terminal" buttons to free memory if needed

## Development Notes

- The application follows MVVM pattern with ReactiveUI
- All UI operations are thread-safe
- Comprehensive error handling and logging
- Extensible architecture for adding new core types
- Built-in help system accessible via Help button
